import { useState, useEffect } from 'react';
import { PERMISSION_CATEGORIES } from '../data/permissionsData';

export const usePermissions = (selectedRole) => {
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedRole) {
      initializePermissions();
    }
  }, [selectedRole]);

  const initializePermissions = () => {
    const rolePermissions = selectedRole?.permissions || [];
    const permissionState = {};

    Object.values(PERMISSION_CATEGORIES).forEach(category => {
      category.permissions.forEach(permission => {
        permissionState[permission.id] = rolePermissions.includes(permission.id);
      });
    });

    setPermissions(permissionState);
  };

  const togglePermission = (permissionId) => {
    setPermissions(prev => ({
      ...prev,
      [permissionId]: !prev[permissionId]
    }));
  };

  const getActivePermissions = () => {
    return Object.entries(permissions)
      .filter(([_, isActive]) => isActive)
      .map(([permissionId]) => permissionId);
  };

  const savePermissions = async () => {
    setLoading(true);
    try {
      const activePermissions = getActivePermissions();
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return activePermissions;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    permissions,
    togglePermission,
    savePermissions,
    getActivePermissions,
    loading
  };
};