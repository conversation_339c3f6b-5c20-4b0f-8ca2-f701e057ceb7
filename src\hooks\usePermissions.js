import { useState, useEffect } from 'react';
import { PERMISSION_CATEGORIES } from '../data/permissionsData';
import rbacService from '../services/rbacService';
import { toast } from 'react-toastify';

export const usePermissions = (selectedRole) => {
  const [permissions, setPermissions] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (selectedRole) {
      initializePermissions();
    }
  }, [selectedRole]);

  const initializePermissions = () => {
    if (!selectedRole) {
      setPermissions({});
      return;
    }

    const rolePermissions = selectedRole.permissions || {};
    const permissionState = {};

    // Handle both array format (frontend) and object format (backend)
    if (Array.isArray(rolePermissions)) {
      // Frontend format - array of permission IDs
      Object.values(PERMISSION_CATEGORIES).forEach(category => {
        category.permissions.forEach(permission => {
          permissionState[permission.id] = rolePermissions.includes(permission.id);
        });
      });
    } else {
      // Backend format - object with permission keys
      Object.values(PERMISSION_CATEGORIES).forEach(category => {
        category.permissions.forEach(permission => {
          permissionState[permission.id] = rolePermissions[permission.id] === true;
        });
      });
    }

    setPermissions(permissionState);
  };

  const togglePermission = (permissionId) => {
    setPermissions(prev => ({
      ...prev,
      [permissionId]: !prev[permissionId]
    }));
  };

  const getActivePermissions = () => {
    return Object.entries(permissions)
      .filter(([_, isActive]) => isActive)
      .map(([permissionId]) => permissionId);
  };

  const savePermissions = async () => {
    setLoading(true);
    try {
      const activePermissions = getActivePermissions();
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      return activePermissions;
    } catch (error) {
      throw error;
    } finally {
      setLoading(false);
    }
  };

  return {
    permissions,
    togglePermission,
    savePermissions,
    getActivePermissions,
    loading
  };
};