import { useState, useEffect } from 'react';
import { DEFAULT_ROLES } from '../data/permissionsData';

export const useRoles = () => {
  const [roles, setRoles] = useState(DEFAULT_ROLES);
  const [selectedRole, setSelectedRole] = useState(DEFAULT_ROLES[0]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchRoles();
  }, []);

  const fetchRoles = async () => {
    setLoading(true);
    try {
      // For now, using mock data. Replace with actual API call when backend is ready
      setRoles(DEFAULT_ROLES);
      setError(null);
    } catch (err) {
      setError('Failed to fetch roles');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const createRole = async (roleData) => {
    try {
      const newRole = {
        id: Date.now(),
        ...roleData,
        userCount: 0,
        createdDate: new Date().toISOString().split('T')[0],
        createdBy: 'Current User',
        lastModified: new Date().toISOString().split('T')[0],
        modifiedBy: 'Current User',
        permissions: []
      };
      
      setRoles(prev => [...prev, newRole]);
      return newRole;
    } catch (err) {
      setError('Failed to create role');
      throw err;
    }
  };

  const updateRole = async (roleId, roleData) => {
    try {
      const updatedRole = {
        ...roleData,
        lastModified: new Date().toISOString().split('T')[0],
        modifiedBy: 'Current User'
      };
      
      setRoles(prev => prev.map(role => 
        role.id === roleId ? { ...role, ...updatedRole } : role
      ));
      
      if (selectedRole?.id === roleId) {
        setSelectedRole(prev => ({ ...prev, ...updatedRole }));
      }
      
      return updatedRole;
    } catch (err) {
      setError('Failed to update role');
      throw err;
    }
  };

  const deleteRole = async (roleId) => {
    try {
      setRoles(prev => prev.filter(role => role.id !== roleId));
      if (selectedRole?.id === roleId) {
        setSelectedRole(roles[0]);
      }
    } catch (err) {
      setError('Failed to delete role');
      throw err;
    }
  };

  return {
    roles,
    selectedRole,
    setSelectedRole,
    loading,
    error,
    createRole,
    updateRole,
    deleteRole,
    fetchRoles
  };
};